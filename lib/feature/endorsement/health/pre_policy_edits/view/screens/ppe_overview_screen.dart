import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_services_initial_screen.dart';
import 'package:acko_flutter/feature/endorsement/core/util/force_login_util.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/core/util/node_router.dart';
import 'package:acko_flutter/feature/endorsement/domain/models/hl_endorsement_copies.dart'
    as hlEndorsementCopies;
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/overview_bloc/ppe_overview_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/overview_bloc/ppe_overview_states.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/review_bloc/ppe_review_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/notification_cards_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_policy_overview_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/bottomsheets/ppe_draft_changes_sheet.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/widgets/call_us_now_ppe.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/widgets/edit_overview_header.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/widgets/policy_overview_details.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/widgets/policy_overview_expansion_tile.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/state_provider/StateProvider.dart';

class PrePolicyEditsOverviewScreen extends StatefulWidget {
  const PrePolicyEditsOverviewScreen({super.key});

  @override
  State<PrePolicyEditsOverviewScreen> createState() =>
      _PrePolicyEditsOverviewScreenState();
}

class _PrePolicyEditsOverviewScreenState
    extends State<PrePolicyEditsOverviewScreen> implements StateListener {
  PPEOverviewCubit? _cubit;
  StateProvider _stateProvider = StateProvider();
  hlEndorsementCopies.PpeOverviewScreen? rcPpeOverviewScreen;

  @override
  void initState() {
    super.initState();
    rcPpeOverviewScreen = HlEndorsementRepository()
        .prePolicyEdits
        .view
        ?.screens
        ?.ppeOverviewScreen;
    _stateProvider.subscribe(this);
    _cubit = BlocProvider.of<PPEOverviewCubit>(context);
    _cubit?.getEditsData(force: true);
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.REFRESH_PPE_OVERVIEW) {
      _cubit?.getEditsData(force: true, showDefaultDraftSheet: false);
    }
  }

  @override
  dispose() {
    super.dispose();
    _stateProvider.dispose(this);
  }

  @override
  Widget build(BuildContext context) {
    if (rcPpeOverviewScreen == null) {
      return SizedBox.shrink();
    }
    return BlocConsumer<PPEOverviewCubit, PPEOverviewState>(
      listenWhen: (prev, curr) =>
          curr is Loaded || curr is Loading || curr is Error,
      listener: (context, state) {
        if (state is Loaded) {
          final modalShown = ForceLoginUtil.showForceLoginModal(
            context,
            state.jmResponse,
          );
          if (modalShown) return;

          context
              .read<PPEOverviewCubit>()
              .triggerPageEvent(HLPageEvents.VIEW_POLICY_APPLICATION_PAGE);

          bool hasDraft = state.draftStatus.isNotNullOrEmpty &&
              (state.draftStatus.equalsIgnoreCase("draft") ||
                  state.draftStatus.equalsIgnoreCase("pending"));

          if (hasDraft &&
              (state.policyOverviewData.isEditable ?? false) &&
              state.showDefaultDraftSheet &&
              (_cubit?.prePolicyEditingResponse?.edit?.editRequest
                      .isNotNullOrEmpty ??
                  false)) {
            context
                .read<PPEOverviewCubit>()
                .triggerPageEvent(HLPageEvents.VIEW_PENDING_DRAFT_PAGE);

            showModalBottomSheet(
              useSafeArea: true,
              isScrollControlled: true,
              barrierColor: color040222.withOpacity(0.8),
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              context: context,
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(24.0))),
              builder: (context) {
                return BlocProvider(
                  create: (context) => PPEReviewCubit(
                      proposalId: _cubit?.proposalId ?? '',
                      prePolicyEditingResponse:
                          _cubit?.prePolicyEditingResponse),
                  child: DraftChangesSheet(),
                );
              },
            );
          }
        }
      },
      buildWhen: (prev, curr) =>
          curr is Loaded || curr is Loading || curr is Error,
      builder: (context, state) {
        if (state is Loaded) {
          if (state.jmResponse?.showForceLogin == true) {
            return _getErrorView(
              context,
              title: HealthConstants.unauthorizedAccess,
              subtitle: HealthConstants.unauthorizedAccessMessage,
              showButton: false,
              assetImage: 'acko_payments/ic_error_alert.svg',
            );
          }

          return _buildLoadedState(
            state.policyOverviewData,
            state.draftStatus,
            state.editPolicyType,
            state.editNotificationCards,
            state.policyStartDate,
          );
        } else if (state is Loading)
          return _getLoadingView();
        else
          return _getErrorView(context);
      },
    );
  }

  Widget _getLoadingView() {
    return Scaffold(body: Center(child: AckoServiceLoadingScreen()));
  }

  Widget _getErrorView(
    BuildContext context, {
    String? title,
    String? subtitle,
    String? btnTitle,
    bool showButton = true,
    String? assetImage,
  }) {
    return Scaffold(
      body: AckoServicesIntiailScreen(
        title: title ?? something_went_wrong,
        subTitle: subtitle ?? api_something_went_wrong_sory,
        btnTitle: btnTitle ?? go_back,
        isOutlinedButton: showButton,
        onTap: () => Navigator.pop(context),
        imgUrl:
            Util.getAssetImage(assetName: assetImage ?? 'ic_bucket_drop.svg'),
      ),
    );
  }

  _buildLoadedState(
      PolicyOverview? policyOverviewData,
      String? draftStatus,
      EditPolicyType editPolicyType,
      List<EditNotificationCardModel?> editNotificationCards,
      String? policyStartDate) {
    return Scaffold(
        body: SingleChildScrollView(
      child: Column(
        children: [
          EditOverviewHeader(
            title: policyOverviewData
                    ?.policyDetails?.formattedOverviewHeaderTitle ??
                '',
            editPolicyType: editPolicyType,
            editNotificationCards: editNotificationCards,
            proposalId: _cubit?.proposalId ?? '',
            policyStartDate: policyStartDate ?? '',
          ),
          if (policyOverviewData?.portingDetails != null) ...[
            SizedBox(
              height: 24,
            ),
            PolicyOverviewExpansionTile(
              title: rcPpeOverviewScreen!.portingDetails ?? '',
              detailsWidget: PolicyOverviewDetails(
                editType: EditType.PORTING,
                policyOverviewData: policyOverviewData,
              ),
              onEdit: () {
                editFlowNavigation(EditType.PORTING, draftStatus);
              },
              isEditable: policyOverviewData?.isEditable,
              analyticalEvent: HLTrackEvents.TAP_PORTING_DETAILS_TAB,
              initiallyExpanded: true,
            ),
          ],
          if (policyOverviewData?.policyDetails != null) ...[
            const SizedBox(height: 24),
            PolicyOverviewExpansionTile(
              title: rcPpeOverviewScreen!.policyDetails ?? '',
              detailsWidget: PolicyOverviewDetails(
                editType: EditType.POLICY,
                policyOverviewData: policyOverviewData,
              ),
              onEdit: () {
                context
                    .read<PPEOverviewCubit>()
                    .triggerTapEvent(HLTrackEvents.TAP_EDIT_POLICY_DETAILS);
                editFlowNavigation(EditType.POLICY, draftStatus);
              },
              isEditable: policyOverviewData?.isEditable,
              analyticalEvent: HLTrackEvents.TAP_POLICY_DETAILS_TAB,
              initiallyExpanded:
                  policyOverviewData?.portingDetails != null ? false : true,
            ),
          ],
          // Check if proposer is self-excluded (not in insured container but exists in users)
          if (_isProposerSelfExcluded(policyOverviewData)) ...[
            const SizedBox(height: 24),
            PolicyOverviewExpansionTile(
              title: 'Proposer details',
              detailsWidget: PolicyOverviewDetails(
                editType: EditType.MEMBER,
                policyOverviewData:
                    _createPolicyOverviewWithSelfExcludedProposer(
                        policyOverviewData),
                showOnlyProposer: true,
              ),
              onEdit: () {
                context
                    .read<PPEOverviewCubit>()
                    .triggerTapEvent(HLTrackEvents.TAP_EDIT_MEMBER_DETAILS);
                editFlowNavigation(EditType.MEMBER, draftStatus);
              },
              isEditable: policyOverviewData?.isEditable,
              analyticalEvent: HLTrackEvents.TAP_MEMBER_DETAILS_TAB,
              initiallyExpanded: false,
            ),
          ],
          if (policyOverviewData?.memberDetails != null) ...[
            const SizedBox(height: 24),
            PolicyOverviewExpansionTile(
              title: rcPpeOverviewScreen!.memberDetails ?? '',
              detailsWidget: PolicyOverviewDetails(
                editType: EditType.MEMBER,
                policyOverviewData: policyOverviewData,
              ),
              onEdit: () {
                context
                    .read<PPEOverviewCubit>()
                    .triggerTapEvent(HLTrackEvents.TAP_EDIT_MEMBER_DETAILS);
                editFlowNavigation(EditType.MEMBER, draftStatus);
              },
              isEditable: policyOverviewData?.isEditable,
              analyticalEvent: HLTrackEvents.TAP_MEMBER_DETAILS_TAB,
              initiallyExpanded: false,
            ),
          ],
          CallUsNowPrePolicyEdits(),
          const SizedBox(height: 24),
        ],
      ),
    ));
  }

  bool _isProposerSelfExcluded(PolicyOverview? policyOverviewData) {
    // Check if proposer exists in users but not in insured container
    // This indicates self-exclusion
    final hasProposerInUsers = _cubit?.prePolicyEditingResponse?.edit?.oldValue
            ?.usersContainer?.usersMap.values.isNotEmpty ??
        false;
    final hasProposerInInsured = policyOverviewData?.proposerDetails != null;

    return hasProposerInUsers && !hasProposerInInsured;
  }

  PolicyOverview? _createPolicyOverviewWithSelfExcludedProposer(
      PolicyOverview? originalData) {
    if (originalData == null) return null;

    // Get proposer details from users.first (similar to edit screen logic)
    final usersContainer =
        _cubit?.prePolicyEditingResponse?.edit?.oldValue?.usersContainer;
    final firstUser = usersContainer?.usersMap.values.firstOrNull;

    if (firstUser == null) return originalData;

    // CRITICAL FIX: Also get proposer data from insureds container to include height/weight
    final insuredContainer =
        _cubit?.prePolicyEditingResponse?.edit?.oldValue?.insuredContainer;
    final proposerInsured = insuredContainer?.insuredMap.values
        .where((insured) =>
            insured.parameters?.parameterMap['user_id']?.value ==
            firstUser.userId)
        .firstOrNull;

    Map<String, dynamic> proposerParameterMap;
    if (proposerInsured != null) {
      // Merge users and insureds data to get complete proposer info including height/weight
      final mergedUsersMap = HealthJourneyManagerUtils().mergeParameterMaps(
        firstUser.parameters.parameterMap,
        proposerInsured.parameters!.parameterMap,
      );
      proposerParameterMap = Map<String, dynamic>.from(mergedUsersMap);
    } else {
      // Fallback to users data only if not found in insureds
      proposerParameterMap =
          Map<String, dynamic>.from(firstUser.parameters.parameterMap);
    }

    // Create MemberDetails from merged data
    final proposerDetails = MemberDetails.fromMap(
      proposerParameterMap,
      true, // isProposer
      insuredNumber: firstUser.userId,
      isSelfExcluded: true,
      wasSelfExcluded: true,
    );

    // Create a new PolicyOverview with the self-excluded proposer
    return PolicyOverview(
      portingDetails: originalData.portingDetails,
      policyDetails: originalData.policyDetails,
      memberDetails: originalData.memberDetails,
      proposerDetails: proposerDetails,
      isEditable: originalData.isEditable,
    );
  }

  editFlowNavigation(EditType editType, String? draftStatus) {
    bool hasDraft = draftStatus.isNotNullOrEmpty &&
        (draftStatus.equalsIgnoreCase("draft") ||
            draftStatus.equalsIgnoreCase("pending"));

    if (hasDraft) {
      context
          .read<PPEOverviewCubit>()
          .triggerPageEvent(HLPageEvents.VIEW_PENDING_DRAFT_PAGE);

      showModalBottomSheet(
        useSafeArea: true,
        isScrollControlled: true,
        barrierColor: color040222.withOpacity(0.8),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(24.0))),
        builder: (context) {
          return BlocProvider(
            create: (context) => PPEReviewCubit(
                proposalId: _cubit?.proposalId ?? '',
                prePolicyEditingResponse: _cubit?.prePolicyEditingResponse),
            child: DraftChangesSheet(),
          );
        },
      );
    } else {
      JourneyManagerRouter().routeNodeToNativeRoutes(
          _cubit!.nextNode ?? PrePolicyEditNodes.EDIT,
          {
            "edit_type": editType,
            "proposal_id": _cubit?.proposalId,
            "data": _cubit?.prePolicyEditingResponse
          },
          context);
    }
  }
}
